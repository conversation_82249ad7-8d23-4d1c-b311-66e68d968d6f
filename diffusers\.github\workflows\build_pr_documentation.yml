name: Build PR Documentation

on:
  pull_request:
    paths:
      - "src/diffusers/**.py"
      - "examples/**"
      - "docs/**"

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  build:
    uses: huggingface/doc-builder/.github/workflows/build_pr_documentation.yml@main
    with:
      commit_sha: ${{ github.event.pull_request.head.sha }}
      pr_number: ${{ github.event.number }}
      install_libgl1: true
      package: diffusers
      languages: en ko zh ja pt
      custom_container: diffusers/diffusers-doc-builder
