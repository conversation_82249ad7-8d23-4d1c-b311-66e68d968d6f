name: Build documentation

on:
  push:
    branches:
      - main
      - doc-builder*
      - v*-release
      - v*-patch
    paths:
      - "src/diffusers/**.py"
      - "examples/**"
      - "docs/**"

jobs:
  build:
    uses: huggingface/doc-builder/.github/workflows/build_main_documentation.yml@main
    with:
      commit_sha: ${{ github.sha }}
      install_libgl1: true
      package: diffusers
      notebook_folder: diffusers_doc
      languages: en ko zh ja pt
      custom_container: diffusers/diffusers-doc-builder
    secrets:
      token: ${{ secrets.HUGGINGFACE_PUSH }}
      hf_token: ${{ secrets.HF_DOC_BUILD_PUSH }}
