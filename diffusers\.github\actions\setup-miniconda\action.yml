name: Set up conda environment for testing

description: Sets up miniconda in your ${RUNNER_TEMP} environment and gives you the ${CONDA_RUN} environment variable so you don't have to worry about polluting non-empeheral runners anymore

inputs:
  python-version:
    description: If set to any value, don't use sudo to clean the workspace
    required: false
    type: string
    default: "3.9"
  miniconda-version:
    description: Miniconda version to install
    required: false
    type: string
    default: "4.12.0"
  environment-file:
    description: Environment file to install dependencies from
    required: false
    type: string
    default: ""

runs:
  using: composite
  steps:
      # Use the same trick from https://github.com/marketplace/actions/setup-miniconda
      # to refresh the cache daily. This is kind of optional though
      - name: Get date
        id: get-date
        shell: bash
        run: echo "today=$(/bin/date -u '+%Y%m%d')d" >> $GITHUB_OUTPUT
      - name: Setup miniconda cache
        id: miniconda-cache
        uses: actions/cache@v2
        with:
          path: ${{ runner.temp }}/miniconda
          key: miniconda-${{ runner.os }}-${{ runner.arch }}-${{ inputs.python-version }}-${{ steps.get-date.outputs.today }}
      - name: Install miniconda (${{ inputs.miniconda-version }})
        if: steps.miniconda-cache.outputs.cache-hit != 'true'
        env:
          MINICONDA_VERSION: ${{ inputs.miniconda-version }}
        shell: bash -l {0}
        run: |
          MINICONDA_INSTALL_PATH="${RUNNER_TEMP}/miniconda"
          mkdir -p "${MINICONDA_INSTALL_PATH}"
          case ${RUNNER_OS}-${RUNNER_ARCH} in
            Linux-X64)
              MINICONDA_ARCH="Linux-x86_64"
              ;;
            macOS-ARM64)
              MINICONDA_ARCH="MacOSX-arm64"
              ;;
            macOS-X64)
              MINICONDA_ARCH="MacOSX-x86_64"
              ;;
            *)
            echo "::error::Platform ${RUNNER_OS}-${RUNNER_ARCH} currently unsupported using this action"
              exit 1
              ;;
          esac
          MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-py39_${MINICONDA_VERSION}-${MINICONDA_ARCH}.sh"
          curl -fsSL "${MINICONDA_URL}" -o "${MINICONDA_INSTALL_PATH}/miniconda.sh"
          bash "${MINICONDA_INSTALL_PATH}/miniconda.sh" -b -u -p "${MINICONDA_INSTALL_PATH}"
          rm -rf "${MINICONDA_INSTALL_PATH}/miniconda.sh"
      - name: Update GitHub path to include miniconda install
        shell: bash
        run: |
          MINICONDA_INSTALL_PATH="${RUNNER_TEMP}/miniconda"
          echo "${MINICONDA_INSTALL_PATH}/bin" >> $GITHUB_PATH
      - name: Setup miniconda env cache (with env file)
        id: miniconda-env-cache-env-file
        if: ${{ runner.os }} == 'macOS' && ${{ inputs.environment-file }} != ''
        uses: actions/cache@v2
        with:
          path: ${{ runner.temp }}/conda-python-${{ inputs.python-version }}
          key: miniconda-env-${{ runner.os }}-${{ runner.arch }}-${{ inputs.python-version }}-${{ steps.get-date.outputs.today }}-${{ hashFiles(inputs.environment-file) }}
      - name: Setup miniconda env cache (without env file)
        id: miniconda-env-cache
        if: ${{ runner.os }} == 'macOS' && ${{ inputs.environment-file }} == ''
        uses: actions/cache@v2
        with:
          path: ${{ runner.temp }}/conda-python-${{ inputs.python-version }}
          key: miniconda-env-${{ runner.os }}-${{ runner.arch }}-${{ inputs.python-version }}-${{ steps.get-date.outputs.today }}
      - name: Setup conda environment with python (v${{ inputs.python-version }})
        if: steps.miniconda-env-cache-env-file.outputs.cache-hit != 'true' && steps.miniconda-env-cache.outputs.cache-hit != 'true'
        shell: bash
        env:
          PYTHON_VERSION: ${{ inputs.python-version }}
          ENV_FILE: ${{ inputs.environment-file }}
        run: |
          CONDA_BASE_ENV="${RUNNER_TEMP}/conda-python-${PYTHON_VERSION}"
          ENV_FILE_FLAG=""
          if [[ -f "${ENV_FILE}" ]]; then
            ENV_FILE_FLAG="--file ${ENV_FILE}"
          elif [[ -n "${ENV_FILE}" ]]; then
            echo "::warning::Specified env file (${ENV_FILE}) not found, not going to include it"
          fi
          conda create \
            --yes \
            --prefix "${CONDA_BASE_ENV}" \
            "python=${PYTHON_VERSION}" \
            ${ENV_FILE_FLAG} \
            cmake=3.22 \
            conda-build=3.21 \
            ninja=1.10 \
            pkg-config=0.29 \
            wheel=0.37
      - name: Clone the base conda environment and update GitHub env
        shell: bash
        env:
          PYTHON_VERSION: ${{ inputs.python-version }}
          CONDA_BASE_ENV: ${{ runner.temp }}/conda-python-${{ inputs.python-version }}
        run: |
          CONDA_ENV="${RUNNER_TEMP}/conda_environment_${GITHUB_RUN_ID}"
          conda create \
            --yes \
            --prefix "${CONDA_ENV}" \
            --clone "${CONDA_BASE_ENV}"
          # TODO: conda-build could not be cloned because it hardcodes the path, so it
          # could not be cached
          conda install --yes -p ${CONDA_ENV} conda-build=3.21
          echo "CONDA_ENV=${CONDA_ENV}" >> "${GITHUB_ENV}"
          echo "CONDA_RUN=conda run -p ${CONDA_ENV} --no-capture-output" >> "${GITHUB_ENV}"
          echo "CONDA_BUILD=conda run -p ${CONDA_ENV} conda-build" >> "${GITHUB_ENV}"
          echo "CONDA_INSTALL=conda install -p ${CONDA_ENV}" >> "${GITHUB_ENV}"
      - name: Get disk space usage and throw an error for low disk space
        shell: bash
        run: |
          echo "Print the available disk space for manual inspection"
          df -h
          # Set the minimum requirement space to 4GB
          MINIMUM_AVAILABLE_SPACE_IN_GB=4
          MINIMUM_AVAILABLE_SPACE_IN_KB=$(($MINIMUM_AVAILABLE_SPACE_IN_GB * 1024 * 1024))
          # Use KB to avoid floating point warning like 3.1GB
          df -k | tr -s ' ' | cut -d' ' -f 4,9 | while read -r LINE;
          do
            AVAIL=$(echo $LINE | cut -f1 -d' ')
            MOUNT=$(echo $LINE | cut -f2 -d' ')
            if [ "$MOUNT" = "/" ]; then
              if [ "$AVAIL" -lt "$MINIMUM_AVAILABLE_SPACE_IN_KB" ]; then
                echo "There is only ${AVAIL}KB free space left in $MOUNT, which is less than the minimum requirement of ${MINIMUM_AVAILABLE_SPACE_IN_KB}KB. Please help create an issue to PyTorch Release Engineering via https://github.com/pytorch/test-infra/issues and provide the link to the workflow run."
                exit 1;
              else
                echo "There is ${AVAIL}KB free space left in $MOUNT, continue"
              fi
            fi
          done
