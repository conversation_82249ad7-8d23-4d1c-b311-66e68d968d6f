#!/usr/bin/env python3
"""
测试条件DDIM扩散模型
验证模型能够正常创建、训练和推理
"""

import torch
import numpy as np
from ddim_diffusion_model import DDIMDiffusionModel, test_ddim_model
from unet import create_diffusion_dataset

def test_conditional_model_creation():
    """测试条件模型创建"""
    print("测试条件DDIM模型创建...")
    
    model = DDIMDiffusionModel(
        sample_size=64,
        in_channels=4,  # 条件模型：4通道输入
        out_channels=2,
        num_train_timesteps=100
    )
    
    print(f"模型输入通道数: {model.in_channels}")
    print(f"模型输出通道数: {model.out_channels}")
    print(f"条件通道数: {model.condition_channels}")
    print("✓ 条件模型创建成功")
    
    return model

def test_forward_pass():
    """测试前向传播"""
    print("\n测试前向传播...")
    
    model = DDIMDiffusionModel(
        sample_size=64,
        in_channels=4,
        out_channels=2,
        num_train_timesteps=100
    )
    
    batch_size = 2
    sample_size = 64
    
    # 创建测试数据
    noisy_images = torch.randn(batch_size, 2, sample_size, sample_size)
    condition = torch.randn(batch_size, 2, sample_size, sample_size)
    timesteps = torch.randint(0, 100, (batch_size,))
    
    # 前向传播
    output = model.forward(noisy_images, timesteps, condition=condition)
    
    print(f"输入形状: {noisy_images.shape}")
    print(f"条件形状: {condition.shape}")
    print(f"输出形状: {output.sample.shape}")
    
    assert output.sample.shape == (batch_size, 2, sample_size, sample_size)
    print("✓ 前向传播测试通过")

def test_training_step():
    """测试训练步骤"""
    print("\n测试训练步骤...")
    
    model = DDIMDiffusionModel(
        sample_size=64,
        in_channels=4,
        out_channels=2,
        num_train_timesteps=100
    )
    
    batch_size = 2
    sample_size = 64
    
    # 创建测试数据
    clean_images = torch.randn(batch_size, 2, sample_size, sample_size)
    condition = torch.randn(batch_size, 2, sample_size, sample_size)
    
    # 训练步骤
    loss = model.training_step(clean_images, condition)
    
    print(f"训练损失: {loss.item():.6f}")
    assert isinstance(loss.item(), float)
    print("✓ 训练步骤测试通过")

def test_sampling():
    """测试采样"""
    print("\n测试采样...")
    
    model = DDIMDiffusionModel(
        sample_size=64,
        in_channels=4,
        out_channels=2,
        num_train_timesteps=100
    )
    
    batch_size = 2
    sample_size = 64
    
    # 创建条件数据
    condition = torch.randn(batch_size, 2, sample_size, sample_size)
    
    # 采样
    samples = model.sample(
        condition=condition,
        device='cpu',
        num_inference_steps=10  # 使用较少步数以加快测试
    )
    
    print(f"条件形状: {condition.shape}")
    print(f"生成样本形状: {samples.shape}")
    
    assert samples.shape == (batch_size, 2, sample_size, sample_size)
    print("✓ 采样测试通过")

def test_with_real_data():
    """使用真实数据测试"""
    print("\n使用真实数据测试...")
    
    try:
        # 尝试加载真实数据
        dataset = create_diffusion_dataset('./training_data/', num_samples=10, shuffle=False)
        
        # 获取一个批次的数据
        input_data, target_data = dataset[0]
        
        print(f"真实数据 - 输入形状: {input_data.shape}")
        print(f"真实数据 - 目标形状: {target_data.shape}")
        
        # 创建模型
        model = DDIMDiffusionModel(
            sample_size=target_data.shape[-1],  # 使用真实数据的尺寸
            in_channels=4,
            out_channels=2,
            num_train_timesteps=100
        )
        
        # 扩展为批次
        input_batch = input_data.unsqueeze(0)
        target_batch = target_data.unsqueeze(0)
        
        # 测试训练步骤
        loss = model.training_step(target_batch, input_batch)
        print(f"真实数据训练损失: {loss.item():.6f}")
        
        # 测试采样
        samples = model.sample(
            condition=input_batch,
            device='cpu',
            num_inference_steps=5
        )
        print(f"真实数据生成样本形状: {samples.shape}")
        
        print("✓ 真实数据测试通过")
        
    except FileNotFoundError:
        print("⚠ 未找到真实数据文件，跳过真实数据测试")
    except Exception as e:
        print(f"⚠ 真实数据测试失败: {e}")

def main():
    """主测试函数"""
    print("开始测试条件DDIM扩散模型...")
    print("=" * 50)
    
    try:
        #test_conditional_model_creation()
        #test_forward_pass()
        #test_training_step()
        #test_sampling()
        test_with_real_data()
        
        print("\n" + "=" * 50)
        print("✅ 所有测试通过！条件DDIM扩散模型工作正常。")
        print("\n现在可以运行以下命令开始训练:")
        print("python train_ddim.py --mode train --data_path training_data_test_32.npz --num_epochs 5 --batch_size 4")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
