name: Update Diffusers metadata

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - update_diffusers_metadata*

jobs:
  update_metadata:
    runs-on: ubuntu-22.04
    defaults:
      run:
        shell: bash -l {0}

    steps:
      - uses: actions/checkout@v3

      - name: Setup environment
        run: |
          pip install --upgrade pip
          pip install datasets pandas
          pip install .[torch]

      - name: Update metadata
        env:
          HF_TOKEN: ${{ secrets.SAYAK_HF_TOKEN }}
        run: |
          python utils/update_metadata.py --commit_sha ${{ github.sha }}
