#!/usr/bin/env python3
"""
DDIM扩散模型训练脚本
使用处理好的输入和输出数据进行训练
"""

import torch
import torch.nn.functional as F
from torch.utils.data import DataLoader
import argparse
import os
from tqdm import tqdm
import matplotlib.pyplot as plt
import numpy as np
from diffusers.optimization import get_cosine_schedule_with_warmup
# 如果没有sklearn，使用简单的数据划分
try:
    from sklearn.model_selection import train_test_split
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    print("sklearn不可用")

from unet import create_diffusion_dataset
from ddim_diffusion_model import DDIMDiffusionModel, load_ddim_model, test_ddim_model

def calculate_metrics(predictions, targets):
    """
    计算评估指标
    
    参数:
    predictions: [batch, 2, N, N] 预测值
    targets: [batch, 2, N, N] 真实值
    
    返回:
    mse: MSE误差
    mare: 平均相对误差
    accuracy: 准确率（相对误差<0.1的比例）
    """
    # 展平张量以便计算
    pred_flat = predictions.view(-1)
    target_flat = targets.view(-1)
    
    # MSE误差
    mse = F.mse_loss(pred_flat, target_flat).item()
    
    # 避免除零错误，添加小的epsilon
    epsilon = 1e-8
    relative_errors = torch.abs(pred_flat - target_flat) / (torch.abs(target_flat) + epsilon)
    
    # 平均相对误差
    mare = relative_errors.mean().item()
    
    # 准确率（相对误差小于0.1的比例）
    accuracy = (relative_errors < 0.1).float().mean().item()
    
    return mse, mare, accuracy

def visualize_relative_errors(predictions, targets, save_path, epoch):
    """
    可视化相对误差分布
    
    参数:
    predictions: [batch, 2, N, N] 预测值
    targets: [batch, 2, N, N] 真实值
    save_path: 保存路径
    epoch: 当前epoch
    """
    # 计算相对误差
    epsilon = 1e-8
    relative_errors = torch.abs(predictions - targets) / (torch.abs(targets) + epsilon)
    
    # 取第一个样本进行可视化
    if len(relative_errors) > 0:
        sample_errors = relative_errors[0]  # [2, N, N]
        
        fig, axes = plt.subplots(1, 2, figsize=(12, 5))
        
        # 上极板相对误差
        im1 = axes[0].imshow(sample_errors[0].cpu().numpy(), cmap='hot', vmin=0, vmax=1)
        axes[0].set_title('Upper Plate - Relative Error')
        axes[0].axis('off')
        plt.colorbar(im1, ax=axes[0], fraction=0.046, pad=0.04)
        
        # 下极板相对误差
        im2 = axes[1].imshow(sample_errors[1].cpu().numpy(), cmap='hot', vmin=0, vmax=1)
        axes[1].set_title('Lower Plate - Relative Error')
        axes[1].axis('off')
        plt.colorbar(im2, ax=axes[1], fraction=0.046, pad=0.04)
        
        plt.tight_layout()
        plt.savefig(os.path.join(save_path, f'relative_errors_epoch_{epoch}.png'), 
                   dpi=150, bbox_inches='tight')
        plt.close()

def train_with_processed_data(
    data_path='training_data_test_32.npz',
    num_samples=None,
    num_epochs=10,
    batch_size=5,
    learning_rate=1e-3,
    device='cuda',
    save_path='ddim_model.pth',
    log_interval=1,
    sample_size=128,
    val_split=0.2
):
    """
    使用处理好的数据训练DDIM模型
    
    参数:
    data_path: 训练数据路径
    num_samples: 使用的样本数量
    num_epochs: 训练轮数
    batch_size: 批次大小
    learning_rate: 学习率
    device: 计算设备
    save_path: 模型保存路径
    log_interval: 日志打印间隔
    sample_size: 样本尺寸
    """
    
    # 设置设备
    device = torch.device(device if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 创建数据集
    print("加载数据集...")
    full_dataset = create_diffusion_dataset(data_path, num_samples=num_samples, shuffle=True)
    
    # 划分训练集和验证集
    dataset_size = len(full_dataset)
    indices = list(range(dataset_size))
    
    if SKLEARN_AVAILABLE:
        train_indices, val_indices = train_test_split(
            indices, test_size=val_split, random_state=42, shuffle=True
        )
    else:
        # 简单的数据划分
        np.random.seed(42)
        np.random.shuffle(indices)
        val_size = int(dataset_size * val_split)
        val_indices = indices[:val_size]
        train_indices = indices[val_size:]
    
    # 创建子数据集
    train_dataset = torch.utils.data.Subset(full_dataset, train_indices)
    val_dataset = torch.utils.data.Subset(full_dataset, val_indices)

    # 数据增强，将数据集大小扩充至100倍
    train_dataset = torch.utils.data.ConcatDataset([train_dataset] * 100)
    val_dataset = torch.utils.data.ConcatDataset([val_dataset] * 100)
    
    print(f"训练集大小: {len(train_dataset)}, 验证集大小: {len(val_dataset)}")
    
    # 创建数据加载器
    train_dataloader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True, num_workers=4)
    val_dataloader = DataLoader(val_dataset, batch_size=batch_size, shuffle=False, num_workers=4)
    
    # 创建条件DDIM模型
    print("创建条件DDIM模型...")
    model = DDIMDiffusionModel(
        sample_size=sample_size,
        in_channels=4,  # 4通道：2通道目标数据 + 2通道条件数据
        out_channels=2,
        num_train_timesteps=1000
    )
    
    model = model.to(device)
    model.train()
    
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 优化器
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.01)
    
    # 学习率调度器
    scheduler = get_cosine_schedule_with_warmup(
        optimizer=optimizer,
        num_warmup_steps=len(train_dataloader) * num_epochs /20,
        num_training_steps=len(train_dataloader) * num_epochs,
    )
    
    # 训练记录
    train_losses = []
    val_mse_scores = []
    val_mare_scores = []
    val_accuracy_scores = []
    log_count = 0
    
    print("开始训练...")
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        epoch_loss = 0.0
        progress_bar = tqdm(train_dataloader, desc=f'Epoch {epoch+1}/{num_epochs}')
        
        for batch_idx, (input_data, target_data) in enumerate(progress_bar):
            # 移动数据到设备
            # input_data: [batch, 2, N, N] 条件数据
            # target_data: [batch, 2, N, N] 目标输出
            input_data = input_data.to(device)
            target_data = target_data.to(device)

            # 条件训练步骤
            loss = model.training_step(target_data, input_data)
            
            # 反向传播
            optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            
            optimizer.step()
            scheduler.step()
            
            # 记录损失
            epoch_loss += loss.item()
            
            # 更新进度条
            progress_bar.set_postfix({
                'loss': f'{loss.item():.4f}',
                'lr': f'{optimizer.param_groups[0]["lr"]:.6f}'
            })
        
        # 计算平均训练损失
        avg_loss = epoch_loss / len(train_dataloader)
        train_losses.append(avg_loss)
        
        # 每个log_interval进行验证和保存
        if (epoch + 1) % log_interval == 0:
            log_count += 1
            log_dir = f"./ddimddim_train_log{log_count}"
            os.makedirs(log_dir, exist_ok=True)
            
            print(f'Epoch [{epoch+1}/{num_epochs}], Training Loss: {avg_loss:.4f}')
            
            # 验证阶段
            model.eval()
            val_predictions = []
            val_targets = []
            
            with torch.no_grad():
                for input_data, target_data in val_dataloader:
                    input_data = input_data.to(device)
                    target_data = target_data.to(device)

                    # 生成预测（使用条件数据和少量推理步数以加快验证）
                    predictions = model.sample(
                        condition=input_data,
                        device=device,
                        num_inference_steps=10  # 验证时使用较少步数
                    )
                    
                    val_predictions.append(predictions)
                    val_targets.append(target_data)
                
                # 合并所有验证数据
                val_predictions = torch.cat(val_predictions, dim=0)
                val_targets = torch.cat(val_targets, dim=0)
                
                # 计算验证指标
                val_mse, val_mare, val_accuracy = calculate_metrics(val_predictions, val_targets)
                val_mse_scores.append(val_mse)
                val_mare_scores.append(val_mare)
                val_accuracy_scores.append(val_accuracy)
                
                print(f'Validation - MSE: {val_mse:.6f}, MARE: {val_mare:.4f}, Accuracy: {val_accuracy:.4f}')
                
                # 可视化相对误差
                visualize_relative_errors(val_predictions, val_targets, log_dir, epoch + 1)
                
                # 生成样本图像（使用验证集中的前4个条件）
                sample_condition = None
                for input_data, _ in val_dataloader:
                    sample_condition = input_data[:4].to(device)
                    break

                if sample_condition is not None:
                    sample_images = model.sample(
                        condition=sample_condition,
                        device=device,
                        num_inference_steps=50
                    )
                else:
                    # 如果没有验证数据，创建随机条件
                    sample_condition = torch.randn(4, 2, sample_size, sample_size, device=device)
                    sample_images = model.sample(
                        condition=sample_condition,
                        device=device,
                        num_inference_steps=50
                    )
                
                # 保存样本图像到log目录（包含条件对比）
                save_conditional_sample_images_to_dir(sample_condition, sample_images, log_dir, epoch + 1)
            
            # 保存当前模型到log目录
            model_save_path = os.path.join(log_dir, f'model_epoch_{epoch+1}.pth')
            torch.save({
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'scheduler_state_dict': scheduler.state_dict(),
                'train_losses': train_losses,
                'val_mse_scores': val_mse_scores,
                'val_mare_scores': val_mare_scores,
                'val_accuracy_scores': val_accuracy_scores,
                'epoch': epoch,
                'config': {
                    'sample_size': model.sample_size,
                    'in_channels': model.in_channels,
                    'out_channels': model.out_channels,
                    'num_train_timesteps': model.num_train_timesteps,
                }
            }, model_save_path)
            
            print(f'模型已保存到: {model_save_path}')
            
            model.train()
    
    # 保存最终模型
    torch.save({
        'model_state_dict': model.state_dict(),
        'optimizer_state_dict': optimizer.state_dict(),
        'scheduler_state_dict': scheduler.state_dict(),
        'train_losses': train_losses,
        'val_mse_scores': val_mse_scores,
        'val_mare_scores': val_mare_scores,
        'val_accuracy_scores': val_accuracy_scores,
        'epoch': num_epochs,
        'config': {
            'sample_size': model.sample_size,
            'in_channels': model.in_channels,
            'out_channels': model.out_channels,
            'num_train_timesteps': model.num_train_timesteps,
        }
    }, save_path)
    
    print(f"训练完成！最终模型已保存到: {save_path}")
    
    # 绘制完整的训练曲线
    plot_comprehensive_training_curves(train_losses, val_mse_scores, val_mare_scores, val_accuracy_scores, log_interval)
    
    return model, train_losses, val_mse_scores, val_mare_scores, val_accuracy_scores

def save_conditional_sample_images_to_dir(condition_images, sample_images, save_dir, epoch):
    """保存条件和生成的样本图像对比到指定目录"""
    fig, axes = plt.subplots(4, 4, figsize=(16, 16))

    for i in range(4):
        # 条件 - 上极板
        axes[0, i].imshow(condition_images[i, 0].cpu().numpy(), cmap='viridis')
        axes[0, i].set_title(f'Condition {i+1} - Upper')
        axes[0, i].axis('off')

        # 条件 - 下极板
        axes[1, i].imshow(condition_images[i, 1].cpu().numpy(), cmap='viridis')
        axes[1, i].set_title(f'Condition {i+1} - Lower')
        axes[1, i].axis('off')

        # 生成 - 上极板
        axes[2, i].imshow(sample_images[i, 0].cpu().numpy(), cmap='viridis')
        axes[2, i].set_title(f'Generated {i+1} - Upper')
        axes[2, i].axis('off')

        # 生成 - 下极板
        axes[3, i].imshow(sample_images[i, 1].cpu().numpy(), cmap='viridis')
        axes[3, i].set_title(f'Generated {i+1} - Lower')
        axes[3, i].axis('off')

    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'conditional_sample_images_epoch_{epoch}.png'),
               dpi=150, bbox_inches='tight')
    plt.close()

def save_sample_images_to_dir(sample_images, save_dir, epoch):
    """保存生成的样本图像到指定目录"""
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    for i in range(4):
        # 上极板
        im1 = axes[0, i].imshow(sample_images[i, 0].cpu().numpy(), cmap='viridis')
        axes[0, i].set_title(f'Sample {i+1} - Upper Plate')
        axes[0, i].axis('off')
        plt.colorbar(im1, ax=axes[0, i], fraction=0.046, pad=0.04)
        
        # 下极板
        im2 = axes[1, i].imshow(sample_images[i, 1].cpu().numpy(), cmap='plasma')
        axes[1, i].set_title(f'Sample {i+1} - Lower Plate')
        axes[1, i].axis('off')
        plt.colorbar(im2, ax=axes[1, i], fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    plt.savefig(os.path.join(save_dir, f'samples_epoch_{epoch}.png'), dpi=150, bbox_inches='tight')
    plt.close()

def save_sample_images(sample_images, epoch):
    """保存生成的样本图像（兼容旧版本）"""
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    for i in range(4):
        # 上极板
        im1 = axes[0, i].imshow(sample_images[i, 0].cpu().numpy(), cmap='viridis')
        axes[0, i].set_title(f'Sample {i+1} - Upper Plate')
        axes[0, i].axis('off')
        plt.colorbar(im1, ax=axes[0, i], fraction=0.046, pad=0.04)
        
        # 下极板
        im2 = axes[1, i].imshow(sample_images[i, 1].cpu().numpy(), cmap='plasma')
        axes[1, i].set_title(f'Sample {i+1} - Lower Plate')
        axes[1, i].axis('off')
        plt.colorbar(im2, ax=axes[1, i], fraction=0.046, pad=0.04)
    
    plt.tight_layout()
    plt.savefig(f'samples_epoch_{epoch}.png', dpi=150, bbox_inches='tight')
    plt.close()

def plot_comprehensive_training_curves(train_losses, val_mse_scores, val_mare_scores, val_accuracy_scores, log_interval):
    """绘制完整的训练曲线"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 训练损失
    epochs = range(1, len(train_losses) + 1)
    axes[0, 0].plot(epochs, train_losses, 'b-', linewidth=2, label='Training Loss')
    axes[0, 0].set_title('Training Loss')
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_yscale('log')
    
    # 验证集MSE
    val_epochs = range(log_interval, len(train_losses) + 1, log_interval)
    axes[0, 1].plot(val_epochs, val_mse_scores, 'r-', linewidth=2, label='Validation MSE')
    axes[0, 1].set_title('Validation MSE')
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('MSE')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_yscale('log')
    
    # 验证集平均相对误差
    axes[1, 0].plot(val_epochs, val_mare_scores, 'g-', linewidth=2, label='Validation MARE')
    axes[1, 0].set_title('Validation Mean Absolute Relative Error')
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('MARE')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # 验证集准确率
    axes[1, 1].plot(val_epochs, val_accuracy_scores, 'm-', linewidth=2, label='Validation Accuracy')
    axes[1, 1].set_title('Validation Accuracy (Relative Error < 0.1)')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Accuracy')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_ylim(0, 1)
    
    plt.tight_layout()
    plt.savefig('ddim_training_curves.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("完整训练曲线已保存到: comprehensive_training_curves.png")

def plot_training_curve(train_losses):
    """绘制训练曲线（兼容旧版本）"""
    plt.figure(figsize=(10, 6))
    plt.plot(train_losses, 'b-', linewidth=2, label='Training Loss')
    plt.title('DDIM Training Loss Curve')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.yscale('log')
    plt.savefig('ddim_training_curve.png', dpi=150, bbox_inches='tight')
    plt.show()
    print("训练曲线已保存到: ddim_training_curve.png")

def evaluate_model(model_path, data_path, device='cuda', num_samples=8):
    """评估训练好的模型"""
    print("加载模型...")
    model, config = load_ddim_model(model_path, device)
    
    print("生成样本...")
    samples = test_ddim_model(model, device=device, num_samples=num_samples)
    
    # 加载真实数据进行对比
    print("加载真实数据进行对比...")
    dataset = create_diffusion_dataset(data_path, num_samples=num_samples)
    real_samples = []
    for i in range(min(num_samples, len(dataset))):
        _, target = dataset[i]
        real_samples.append(target)
    
    real_samples = torch.stack(real_samples)
    
    # 对比可视化
    fig, axes = plt.subplots(4, num_samples, figsize=(2*num_samples, 8))
    
    for i in range(num_samples):
        # 生成样本 - 上极板
        axes[0, i].imshow(samples[i, 0].cpu().numpy(), cmap='viridis')
        axes[0, i].set_title(f'Generated {i+1} - Upper')
        axes[0, i].axis('off')
        
        # 生成样本 - 下极板
        axes[1, i].imshow(samples[i, 1].cpu().numpy(), cmap='plasma')
        axes[1, i].set_title(f'Generated {i+1} - Lower')
        axes[1, i].axis('off')
        
        if i < len(real_samples):
            # 真实样本 - 上极板
            axes[2, i].imshow(real_samples[i, 0].cpu().numpy(), cmap='viridis')
            axes[2, i].set_title(f'Real {i+1} - Upper')
            axes[2, i].axis('off')
            
            # 真实样本 - 下极板
            axes[3, i].imshow(real_samples[i, 1].cpu().numpy(), cmap='plasma')
            axes[3, i].set_title(f'Real {i+1} - Lower')
            axes[3, i].axis('off')
    
    plt.tight_layout()
    plt.savefig('model_evaluation.png', dpi=150, bbox_inches='tight')
    plt.show()
    
    return samples, real_samples

def main():
    parser = argparse.ArgumentParser(description='训练DDIM扩散模型')
    parser.add_argument('--data_path', type=str, default='./training_data', 
                       help='训练数据路径')
    parser.add_argument('--num_samples', type=int, default=None, 
                       help='使用的样本数量')
    parser.add_argument('--num_epochs', type=int, default=10, 
                       help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=100, 
                       help='批次大小')
    parser.add_argument('--learning_rate', type=float, default=0.005, 
                       help='学习率')
    parser.add_argument('--device', type=str, default='cuda', 
                       help='计算设备')
    parser.add_argument('--save_path', type=str, default='ddim_model.pth', 
                       help='模型保存路径')
    parser.add_argument('--log_interval', type=int, default=1, 
                       help='日志打印间隔epoch')
    parser.add_argument('--sample_size', type=int, default=64, 
                       help='样本尺寸')
    parser.add_argument('--val_split', type=float, default=0.1, 
                       help='验证集比例')
    parser.add_argument('--mode', type=str, default='train', choices=['train', 'eval'],
                       help='运行模式: train或eval')
    parser.add_argument('--model_path', type=str, default='ddim_model_best.pth',
                       help='评估时使用的模型路径')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        # 训练模式
        results = train_with_processed_data(
            data_path=args.data_path,
            num_samples=args.num_samples,
            num_epochs=args.num_epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            device=args.device,
            save_path=args.save_path,
            log_interval=args.log_interval,
            sample_size=args.sample_size,
            val_split=args.val_split
        )
        
        if len(results) == 5:
            model, train_losses, val_mse_scores, val_mare_scores, val_accuracy_scores = results
            print("训练完成！")
            print(f"最终训练损失: {train_losses[-1]:.6f}")
            if val_mse_scores:
                print(f"最终验证MSE: {val_mse_scores[-1]:.6f}")
                print(f"最终验证MARE: {val_mare_scores[-1]:.4f}")
                print(f"最终验证准确率: {val_accuracy_scores[-1]:.4f}")
        else:
            print("训练完成！")
        
    elif args.mode == 'eval':
        # 评估模式
        if not os.path.exists(args.model_path):
            print(f"模型文件不存在: {args.model_path}")
            return
        
        samples, real_samples = evaluate_model(
            model_path=args.model_path,
            data_path=args.data_path,
            device=args.device,
            num_samples=8
        )
        
        print("评估完成！")

if __name__ == "__main__":
    main()