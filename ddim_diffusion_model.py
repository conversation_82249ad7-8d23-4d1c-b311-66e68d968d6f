import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
from tqdm import tqdm
import matplotlib.pyplot as plt


try:
    from diffusers import UNet2DModel, DDIMScheduler, DDIMPipeline
    from diffusers.optimization import get_cosine_schedule_with_warmup
    DIFFUSERS_AVAILABLE = True
    print("使用diffusers库实现DDIM模型")
except ImportError:
    DIFFUSERS_AVAILABLE = False
    print("diffusers库不可用，请安装: pip install diffusers")

class DDIMDiffusionModel(nn.Module):
    """
    基于diffusers库的条件DDIM扩散模型
    输入: [batch, 2, N, N] (目标数据) + [batch, 2, N, N] (条件数据)
    输出: [batch, 2, N, N]
    """
    def __init__(self,
                 sample_size=64,
                 in_channels=4,  # 修改为4通道：2通道目标数据 + 2通道条件数据
                 out_channels=2,
                 layers_per_block=2,
                 block_out_channels=None,
                 down_block_types=None,
                 up_block_types=None,
                 num_train_timesteps=1000,
                 beta_start=0.0001,
                 beta_end=0.02,
                 beta_schedule="linear"):
        
        super(DDIMDiffusionModel, self).__init__()

        if not DIFFUSERS_AVAILABLE:
            raise ImportError("需要安装diffusers库: pip install diffusers")

        self.sample_size = sample_size
        self.in_channels = in_channels
        self.out_channels = out_channels
        self.num_train_timesteps = num_train_timesteps
        self.condition_channels = 2  # 条件数据的通道数
        
        # 根据sample_size自动配置网络架构
        if block_out_channels is None or down_block_types is None or up_block_types is None:
            block_out_channels, down_block_types, up_block_types = self._get_default_config(sample_size)
        
        # 创建UNet模型
        try:
            self.unet = UNet2DModel(
                sample_size=sample_size,
                in_channels=in_channels,
                out_channels=out_channels,
                layers_per_block=layers_per_block,
                block_out_channels=block_out_channels,
                down_block_types=down_block_types,
                up_block_types=up_block_types,
            )
        except Exception as e:
            print(f"使用默认配置创建UNet时出错: {e}")
        
        # 创建DDIM调度器
        self.scheduler = DDIMScheduler(
            num_train_timesteps=num_train_timesteps,
            beta_start=beta_start,
            beta_end=beta_end,
            beta_schedule=beta_schedule,
            clip_sample=False,
            set_alpha_to_one=False,
            steps_offset=1,
        )
        
        # 设置推理步数
        self.scheduler.set_timesteps(50)  # 默认50步推理
    
    def _get_default_config(self, sample_size):
        """根据sample_size自动配置网络架构"""
        if sample_size <= 32:
            # 小尺寸配置
            block_out_channels = (128, 256, 512)
            down_block_types = (
                "DownBlock2D",
                "DownBlock2D", 
                "DownBlock2D",
            )
            up_block_types = (
                "UpBlock2D",
                "UpBlock2D",
                "UpBlock2D",
            )
        else:
            # 大尺寸配置 (32+)
            block_out_channels = (64, 128, 256, 512, 1024)
            down_block_types = (
                "DownBlock2D",
                "DownBlock2D",
                "DownBlock2D",
                "AttnDownBlock2D",
                "DownBlock2D",
            )
            up_block_types = (
                "UpBlock2D",
                "AttnUpBlock2D",
                "UpBlock2D",
                "UpBlock2D",
                "UpBlock2D",
            )
        
        return block_out_channels, down_block_types, up_block_types
        
    def forward(self, sample, timestep, condition=None, return_dict=True):
        """
        前向传播

        参数:
        sample: [batch, 2, N, N] 噪声图像
        timestep: 时间步
        condition: [batch, 2, N, N] 条件数据（input_data）
        return_dict: 是否返回字典格式
        """
        if condition is not None:
            # 将条件数据与噪声图像拼接
            sample_with_condition = torch.cat([sample, condition], dim=1)  # [batch, 4, N, N]
        else:
            # 如果没有条件，用零填充
            batch_size = sample.shape[0]
            device = sample.device
            zero_condition = torch.zeros(batch_size, self.condition_channels,
                                       sample.shape[2], sample.shape[3],
                                       device=device, dtype=sample.dtype)
            sample_with_condition = torch.cat([sample, zero_condition], dim=1)

        return self.unet(sample_with_condition, timestep, return_dict=return_dict)
    
    def training_step(self, clean_images, condition):
        """
        条件训练步骤

        参数:
        clean_images: [batch, 2, N, N] 干净的目标图像
        condition: [batch, 2, N, N] 条件数据（input_data）
        """
        batch_size = clean_images.shape[0]
        device = clean_images.device

        # 随机采样时间步
        timesteps = torch.randint(
            0, self.scheduler.config.num_train_timesteps,
            (batch_size,), device=device
        ).long()

        # 生成随机噪声
        noise = torch.randn_like(clean_images)

        # 添加噪声
        noisy_images = self.scheduler.add_noise(clean_images, noise, timesteps)

        # 预测噪声（传入条件）
        noise_pred = self.forward(noisy_images, timesteps, condition=condition).sample

        # 计算损失
        loss = F.mse_loss(noise_pred, noise)
        return loss
    
    @torch.no_grad()
    def sample(self, condition, batch_size=None, device='cpu', num_inference_steps=50, generator=None):
        """
        使用条件DDIM采样生成新数据

        参数:
        condition: [batch, 2, N, N] 条件数据（input_data）
        batch_size: 批次大小（如果为None，从condition推断）
        device: 计算设备
        num_inference_steps: 推理步数
        generator: 随机数生成器
        """
        if batch_size is None:
            batch_size = condition.shape[0]

        # 确保条件数据在正确的设备上
        condition = condition.to(device)

        # 设置推理步数
        self.scheduler.set_timesteps(num_inference_steps)

        # 从纯噪声开始（只有目标数据的通道数）
        shape = (batch_size, self.out_channels, self.sample_size, self.sample_size)
        image = torch.randn(shape, device=device, generator=generator)

        # DDIM采样循环
        for t in tqdm(self.scheduler.timesteps):
            # 预测噪声（传入条件）
            noise_pred = self.forward(image, t, condition=condition).sample

            # 计算前一个时间步的图像
            image = self.scheduler.step(noise_pred, t, image).prev_sample

        return image
    
    def create_pipeline(self):
        """创建DDIM推理管道"""
        return DDIMPipeline(unet=self.unet, scheduler=self.scheduler)


def load_ddim_model(model_path, device='cuda'):
    """加载训练好的条件DDIM模型"""
    checkpoint = torch.load(model_path, map_location=device)
    config = checkpoint['config']

    # 创建模型（确保使用正确的通道数）
    model = DDIMDiffusionModel(
        sample_size=config['sample_size'],
        in_channels=config.get('in_channels', 4),  # 默认4通道（条件模型）
        out_channels=config['out_channels'],
        num_train_timesteps=config['num_train_timesteps'],
    )

    # 加载权重
    model.load_state_dict(checkpoint['model_state_dict'])
    model = model.to(device)

    return model, config

def test_ddim_model(model, condition, device='cuda', num_inference_steps=50):
    """
    测试条件DDIM模型生成效果

    参数:
    model: 训练好的条件DDIM模型
    condition: [batch, 2, N, N] 条件数据
    device: 计算设备
    num_inference_steps: 推理步数
    """
    model.eval()
    num_samples = condition.shape[0]

    with torch.no_grad():
        # 生成样本
        samples = model.sample(
            condition=condition,
            device=device,
            num_inference_steps=num_inference_steps
        )

        # 可视化结果：条件、生成结果对比
        fig, axes = plt.subplots(4, num_samples, figsize=(2*num_samples, 8))

        for i in range(num_samples):
            # 条件数据 - 上极板
            axes[0, i].imshow(condition[i, 0].cpu().numpy(), cmap='viridis')
            axes[0, i].set_title(f'Condition {i+1} - Upper')
            axes[0, i].axis('off')

            # 条件数据 - 下极板
            axes[1, i].imshow(condition[i, 1].cpu().numpy(), cmap='viridis')
            axes[1, i].set_title(f'Condition {i+1} - Lower')
            axes[1, i].axis('off')

            # 生成结果 - 上极板
            axes[2, i].imshow(samples[i, 0].cpu().numpy(), cmap='viridis')
            axes[2, i].set_title(f'Generated {i+1} - Upper')
            axes[2, i].axis('off')

            # 生成结果 - 下极板
            axes[3, i].imshow(samples[i, 1].cpu().numpy(), cmap='viridis')
            axes[3, i].set_title(f'Generated {i+1} - Lower')
            axes[3, i].axis('off')

        plt.tight_layout()
        plt.savefig('conditional_ddim_test_samples.png', dpi=150, bbox_inches='tight')
        plt.show()

        return samples
