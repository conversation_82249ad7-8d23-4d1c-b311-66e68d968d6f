name: Test, build, and push Docker images

on:
  pull_request: # During PRs, we just check if the changes Dockerfiles can be successfully built
    branches:
      - main
    paths:
      - "docker/**"
  workflow_dispatch:
  schedule:
    - cron: "0 0 * * *" # every day at midnight

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

env:
  REGISTRY: diffusers
  CI_SLACK_CHANNEL: ${{ secrets.CI_DOCKER_CHANNEL }}

jobs:
  test-build-docker-images:
    runs-on:
      group: aws-general-8-plus
    if: github.event_name == 'pull_request'
    steps:
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1

      - name: Check out code
        uses: actions/checkout@v3

      - name: Find Changed Dockerfiles
        id: file_changes
        uses: jitterbit/get-changed-files@v1
        with:
          format: "space-delimited"
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Changed Docker Images
        env: 
          CHANGED_FILES: ${{ steps.file_changes.outputs.all }}
        run: |
          echo "$CHANGED_FILES"
          for FILE in $CHANGED_FILES; do 
            # skip anything that isn't still on disk
            if [[ ! -f "$FILE" ]]; then
              echo "Skipping removed file $FILE"
              continue
            fi           
            if [[ "$FILE" == docker/*Dockerfile ]]; then
              DOCKER_PATH="${FILE%/Dockerfile}"
              DOCKER_TAG=$(basename "$DOCKER_PATH")
              echo "Building Docker image for $DOCKER_TAG"
              docker build -t "$DOCKER_TAG" "$DOCKER_PATH"
            fi
          done
        if: steps.file_changes.outputs.all != ''

  build-and-push-docker-images:
    runs-on:
      group: aws-general-8-plus
    if: github.event_name != 'pull_request'

    permissions:
      contents: read
      packages: write

    strategy:
      fail-fast: false
      matrix:
        image-name:
          - diffusers-pytorch-cpu
          - diffusers-pytorch-cuda
          - diffusers-pytorch-cuda
          - diffusers-pytorch-xformers-cuda
          - diffusers-pytorch-minimum-cuda
          - diffusers-doc-builder

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ env.REGISTRY }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Build and push
        uses: docker/build-push-action@v3
        with:
          no-cache: true
          context: ./docker/${{ matrix.image-name }}
          push: true
          tags: ${{ env.REGISTRY }}/${{ matrix.image-name }}:latest

      - name: Post to a Slack channel
        id: slack
        uses: huggingface/hf-workflows/.github/actions/post-slack@main
        with:
          # Slack channel id, channel name, or user id to post message.
          # See also: https://api.slack.com/methods/chat.postMessage#channels
          slack_channel: ${{ env.CI_SLACK_CHANNEL }}
          title: "🤗 Results of the ${{ matrix.image-name }} Docker Image build"
          status: ${{ job.status }}
          slack_token: ${{ secrets.SLACK_CIFEEDBACK_BOT_TOKEN }}
