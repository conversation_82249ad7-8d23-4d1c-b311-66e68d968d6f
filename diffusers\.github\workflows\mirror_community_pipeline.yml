name: Mirror Community Pipeline

on:
  # Push changes on the main branch
  push:
    branches:
      - main
    paths:
      - 'examples/community/**.py'

    # And on tag creation (e.g. `v0.28.1`)
    tags:
      - '*'

  # Manual trigger with ref input
  workflow_dispatch:
    inputs:
      ref:
        description: "Either 'main' or a tag ref"
        required: true
        default: 'main'

jobs:
  mirror_community_pipeline:
    env:
      SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL_COMMUNITY_MIRROR }}

    runs-on: ubuntu-22.04
    steps:
      # Checkout to correct ref
      #   If workflow dispatch
      #     If ref is 'main', set:
      #       CHECKOUT_REF=refs/heads/main
      #       PATH_IN_REPO=main
      #     Else it must be a tag. Set:
      #       CHECKOUT_REF=refs/tags/{tag}
      #       PATH_IN_REPO={tag}
      #   If not workflow dispatch
      #     If ref is 'refs/heads/main' => set 'main'
      #     Else it must be a tag => set {tag}
      - name: Set checkout_ref and path_in_repo
        run: |
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            if [ -z "${{ github.event.inputs.ref }}" ]; then
              echo "Error: Missing ref input"
              exit 1
            elif [ "${{ github.event.inputs.ref }}" == "main" ]; then
              echo "CHECKOUT_REF=refs/heads/main" >> $GITHUB_ENV
              echo "PATH_IN_REPO=main" >> $GITHUB_ENV
            else
              echo "CHECKOUT_REF=refs/tags/${{ github.event.inputs.ref }}" >> $GITHUB_ENV
              echo "PATH_IN_REPO=${{ github.event.inputs.ref }}" >> $GITHUB_ENV
            fi
          elif [ "${{ github.ref }}" == "refs/heads/main" ]; then
            echo "CHECKOUT_REF=${{ github.ref }}" >> $GITHUB_ENV
            echo "PATH_IN_REPO=main" >> $GITHUB_ENV
          else
            # e.g. refs/tags/v0.28.1 -> v0.28.1
            echo "CHECKOUT_REF=${{ github.ref }}" >> $GITHUB_ENV
            echo "PATH_IN_REPO=$(echo ${{ github.ref }} | sed 's/^refs\/tags\///')" >> $GITHUB_ENV
          fi
      - name: Print env vars
        run: |
          echo "CHECKOUT_REF: ${{ env.CHECKOUT_REF }}"
          echo "PATH_IN_REPO: ${{ env.PATH_IN_REPO }}"
      - uses: actions/checkout@v3
        with:
          ref: ${{ env.CHECKOUT_REF }}

      # Setup + install dependencies
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install --upgrade huggingface_hub

      # Check secret is set
      - name: whoami
        run: hf auth whoami
        env:
            HF_TOKEN: ${{ secrets.HF_TOKEN_MIRROR_COMMUNITY_PIPELINES }}

      # Push to HF! (under subfolder based on checkout ref)
      # https://huggingface.co/datasets/diffusers/community-pipelines-mirror
      - name: Mirror community pipeline to HF
        run: hf upload diffusers/community-pipelines-mirror ./examples/community ${PATH_IN_REPO} --repo-type dataset
        env:
            PATH_IN_REPO: ${{ env.PATH_IN_REPO }}
            HF_TOKEN: ${{ secrets.HF_TOKEN_MIRROR_COMMUNITY_PIPELINES }}

      - name: Report success status
        if: ${{ success() }}
        run: |
          pip install requests && python utils/notify_community_pipelines_mirror.py --status=success

      - name: Report failure status
        if: ${{ failure() }}
        run: |
          pip install requests && python utils/notify_community_pipelines_mirror.py --status=failure